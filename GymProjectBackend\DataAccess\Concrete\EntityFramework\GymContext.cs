﻿﻿using Core.Entities.Concrete;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;

namespace DataAccess.Concrete.EntityFramework
{
    public class GymContext:DbContext
    {
        // Constructor injection ile DbContextOptions alıyoruz
        public GymContext(DbContextOptions<GymContext> options) : base(options)
        {
        }

        // Parametre olmayan constructor (geriye dönük uyumluluk için)
        public GymContext()
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // Eğer options zaten yapılandırılmışsa (DI'dan geliyorsa), tekrar yapılandırma
            if (!optionsBuilder.IsConfigured)
            {
                // appsettings.json dosyasını oku
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Environment anahtarını oku
                var environment = configuration["Environment"] ?? "dev";

                // Environment'a göre connection string'i al
                var connectionString = configuration[$"ConnectionStrings:{environment}"];

                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new InvalidOperationException($"'{environment}' environment'ı için connection string bulunamadı!");
                }

                optionsBuilder.UseSqlServer(connectionString);
            }
        }
        public DbSet<City> Cities { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<CompanyAdress> CompanyAdresses { get; set; }
        public DbSet<EntryExitHistory> EntryExitHistories { get; set; }
        public DbSet<Member> Members { get; set; }
        public DbSet<Membership> Memberships { get; set; }
        public DbSet<MembershipType> MembershipTypes { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Town> Towns { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<UserCompany> UserCompanies { get; set; }
        public DbSet<OperationClaim> OperationClaims { get; set; }
        public DbSet<UserOperationClaim> UserOperationClaims { get; set; }
        public DbSet<CompanyUser > CompanyUsers { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<DebtPayment> DebtPayments { get; set; }
        public DbSet<RemainingDebt> RemainingDebts { get; set; }
        public DbSet<UserDevice> UserDevices { get; set; }
        public DbSet<MembershipFreezeHistory> MembershipFreezeHistory { get; set; }

        public DbSet<LicensePackage> LicensePackages { get; set; }
        public DbSet<UserLicense> UserLicenses { get; set; }
        public DbSet<LicenseTransaction> LicenseTransactions { get; set; }
        public DbSet<Expense> Expenses { get; set; } // Giderler tablosu eklendi

        // Egzersiz Sistemi Tabloları
        public DbSet<ExerciseCategory> ExerciseCategories { get; set; }
        public DbSet<SystemExercise> SystemExercises { get; set; }
        public DbSet<CompanyExercise> CompanyExercises { get; set; }

        // Antrenman Programı Sistemi Tabloları
        public DbSet<WorkoutProgramTemplate> WorkoutProgramTemplates { get; set; }
        public DbSet<WorkoutProgramDay> WorkoutProgramDays { get; set; }
        public DbSet<WorkoutProgramExercise> WorkoutProgramExercises { get; set; }
        public DbSet<MemberWorkoutProgram> MemberWorkoutPrograms { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Decimal precision ayarları (SQL Server için)
            // Payment tablosu
            modelBuilder.Entity<Payment>()
                .Property(p => p.PaymentAmount)
                .HasPrecision(18, 2);

            // DebtPayment tablosu
            modelBuilder.Entity<DebtPayment>()
                .Property(dp => dp.PaidAmount)
                .HasPrecision(18, 2);

            // RemainingDebt tablosu
            modelBuilder.Entity<RemainingDebt>()
                .Property(rd => rd.OriginalAmount)
                .HasPrecision(18, 2);

            modelBuilder.Entity<RemainingDebt>()
                .Property(rd => rd.RemainingAmount)
                .HasPrecision(18, 2);

            // Expense tablosu
            modelBuilder.Entity<Expense>()
                .Property(e => e.Amount)
                .HasPrecision(18, 2);

            // MembershipType tablosu
            modelBuilder.Entity<MembershipType>()
                .Property(mt => mt.Price)
                .HasPrecision(18, 2);

            // Product tablosu
            modelBuilder.Entity<Product>()
                .Property(p => p.Price)
                .HasPrecision(18, 2);

            // LicensePackage tablosu
            modelBuilder.Entity<LicensePackage>()
                .Property(lp => lp.Price)
                .HasPrecision(18, 2);

            // LicenseTransaction tablosu
            modelBuilder.Entity<LicenseTransaction>()
                .Property(lt => lt.Amount)
                .HasPrecision(18, 2);

            // Transaction tablosu
            modelBuilder.Entity<Transaction>()
                .Property(t => t.Amount)
                .HasPrecision(18, 2);
        }
    }
}
