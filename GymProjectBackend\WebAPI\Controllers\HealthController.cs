using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Diagnostics;
using DataAccess.Concrete.EntityFramework;
using Microsoft.EntityFrameworkCore;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class HealthController : ControllerBase
    {
        private readonly HealthCheckService _healthCheckService;
        private readonly GymContext _gymContext;

        public HealthController(HealthCheckService healthCheckService, GymContext gymContext)
        {
            _healthCheckService = healthCheckService;
            _gymContext = gymContext;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var report = await _healthCheckService.CheckHealthAsync();
            
            var result = new
            {
                Status = report.Status.ToString(),
                TotalDuration = report.TotalDuration.TotalMilliseconds,
                Checks = report.Entries.Select(e => new
                {
                    Name = e.Key,
                    Status = e.Value.Status.ToString(),
                    Duration = e.Value.Duration.TotalMilliseconds,
                    Description = e.Value.Description,
                    Data = e.Value.Data
                })
            };

            return report.Status == HealthStatus.Healthy ? Ok(result) : StatusCode(503, result);
        }

        [HttpGet("detailed")]
        public async Task<IActionResult> GetDetailed()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                // System bilgileri
                var process = Process.GetCurrentProcess();
                var systemInfo = new
                {
                    MachineName = Environment.MachineName,
                    ProcessorCount = Environment.ProcessorCount,
                    OSVersion = Environment.OSVersion.ToString(),
                    WorkingSet = Math.Round(process.WorkingSet64 / (1024.0 * 1024.0), 2),
                    PrivateMemory = Math.Round(process.PrivateMemorySize64 / (1024.0 * 1024.0), 2),
                    ThreadCount = process.Threads.Count,
                    StartTime = process.StartTime,
                    TotalProcessorTime = process.TotalProcessorTime.TotalMilliseconds
                };

                // Memory bilgileri
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var memoryInfo = new
                {
                    TotalMemoryMB = Math.Round(GC.GetTotalMemory(false) / (1024.0 * 1024.0), 2),
                    Gen0Collections = GC.CollectionCount(0),
                    Gen1Collections = GC.CollectionCount(1),
                    Gen2Collections = GC.CollectionCount(2)
                };

                // Database bağlantı testi
                var dbConnectionTest = await TestDatabaseConnection();

                // Health check raporu
                var healthReport = await _healthCheckService.CheckHealthAsync();

                stopwatch.Stop();

                var result = new
                {
                    Timestamp = DateTime.UtcNow,
                    Status = healthReport.Status.ToString(),
                    TotalCheckDuration = healthReport.TotalDuration.TotalMilliseconds,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    SystemInfo = systemInfo,
                    MemoryInfo = memoryInfo,
                    DatabaseConnection = dbConnectionTest,
                    HealthChecks = healthReport.Entries.Select(e => new
                    {
                        Name = e.Key,
                        Status = e.Value.Status.ToString(),
                        Duration = e.Value.Duration.TotalMilliseconds,
                        Description = e.Value.Description,
                        Exception = e.Value.Exception?.Message,
                        Data = e.Value.Data
                    })
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Status = "Unhealthy",
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        [HttpGet("database")]
        public async Task<IActionResult> TestDatabase()
        {
            var result = await TestDatabaseConnection();
            return result.IsHealthy ? Ok(result) : StatusCode(503, result);
        }

        private async Task<object> TestDatabaseConnection()
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                // Basit bir sorgu ile bağlantıyı test et
                var userCount = await _gymContext.Users.CountAsync();
                var companyCount = await _gymContext.Companies.CountAsync();
                
                stopwatch.Stop();

                return new
                {
                    IsHealthy = true,
                    Status = "Healthy",
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    UserCount = userCount,
                    CompanyCount = companyCount,
                    ConnectionString = _gymContext.Database.GetConnectionString()?.Substring(0, 50) + "...",
                    DatabaseProvider = _gymContext.Database.ProviderName
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return new
                {
                    IsHealthy = false,
                    Status = "Unhealthy",
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Error = ex.Message,
                    ConnectionString = _gymContext.Database.GetConnectionString()?.Substring(0, 50) + "..."
                };
            }
        }

        [HttpGet("performance")]
        public async Task<IActionResult> GetPerformanceMetrics()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                // CPU ve Memory metrikleri
                var process = Process.GetCurrentProcess();
                var beforeCpu = process.TotalProcessorTime;
                var beforeMemory = GC.GetTotalMemory(false);
                
                // Kısa bir işlem yap
                await Task.Delay(100);
                
                var afterCpu = process.TotalProcessorTime;
                var afterMemory = GC.GetTotalMemory(false);
                
                stopwatch.Stop();

                var result = new
                {
                    Timestamp = DateTime.UtcNow,
                    Performance = new
                    {
                        ResponseTimeMs = stopwatch.ElapsedMilliseconds,
                        CpuUsageMs = (afterCpu - beforeCpu).TotalMilliseconds,
                        MemoryUsageMB = Math.Round((afterMemory - beforeMemory) / (1024.0 * 1024.0), 2),
                        TotalMemoryMB = Math.Round(afterMemory / (1024.0 * 1024.0), 2),
                        WorkingSetMB = Math.Round(process.WorkingSet64 / (1024.0 * 1024.0), 2),
                        ThreadCount = process.Threads.Count,
                        HandleCount = process.HandleCount
                    },
                    GarbageCollection = new
                    {
                        Gen0Collections = GC.CollectionCount(0),
                        Gen1Collections = GC.CollectionCount(1),
                        Gen2Collections = GC.CollectionCount(2)
                    }
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }
    }
}
