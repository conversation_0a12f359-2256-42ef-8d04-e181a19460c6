using System;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Business.Abstract;
using Business.Concrete;
using Business.DependencyResolvers.Autofac;
using Core.Utilities.IoC;
using Core.Utilities.Security.Encryption;
using Core.Utilities.Security.JWT;
using Core.Utilities.Security.Environment;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Core.Extensions;
using Core.DependencyResolvers;
using Core.Extentions;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using System.Diagnostics;
using AspNetCoreRateLimit;
using Microsoft.EntityFrameworkCore;
using HealthChecks.UI.Client;
using Microsoft.Extensions.Diagnostics.HealthChecks;

var builder = WebApplication.CreateBuilder(args);

builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>(builder =>
    {
        builder.RegisterModule(new AutofacBusinessModule());
    });

// Environment anahtarını oku
var environment = builder.Configuration["Environment"] ?? "dev";

// Environment'a göre connection string'i al ve DbContext'i kaydet
var connectionString = builder.Configuration[$"ConnectionStrings:{environment}"];
if (string.IsNullOrEmpty(connectionString))
{
    throw new InvalidOperationException($"'{environment}' environment'ı için connection string bulunamadı!");
}

// DbContext'i Scoped olarak kaydet (Request başına bir instance)
// Connection pooling optimizasyonları ile
builder.Services.AddDbContext<GymContext>(options =>
{
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        // Connection timeout ayarları
        sqlOptions.CommandTimeout(30); // 30 saniye command timeout

        // Connection resiliency (bağlantı hatalarında retry)
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: 3,
            maxRetryDelay: TimeSpan.FromSeconds(5),
            errorNumbersToAdd: null);
    });

    // Development ortamında sensitive data logging
    if (builder.Environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
}, ServiceLifetime.Scoped);

// Environment'a göre CORS ayarlarını al
var allowedOrigins = builder.Configuration.GetSection($"AllowedOrigins:{environment}").Get<string[]>() ?? new[] { "http://localhost:4200" };

// CORS yapılandırması
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins",
        policy =>
        {
            policy.WithOrigins(allowedOrigins)
                   .AllowAnyHeader()
                   .AllowAnyMethod()
                   .AllowCredentials();
        });
});

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddMemoryCache();
builder.Services.AddHttpContextAccessor();

// Health Checks ekle
builder.Services.AddHealthChecks()
    .AddDbContextCheck<GymContext>("database")
    .AddSqlServer(connectionString, name: "sqlserver")
    .AddCheck("memory", () =>
    {
        var allocatedBytes = GC.GetTotalMemory(false);
        var allocatedMB = allocatedBytes / (1024 * 1024);

        return allocatedMB < 1024
            ? HealthCheckResult.Healthy($"Memory usage: {allocatedMB} MB")
            : HealthCheckResult.Degraded($"High memory usage: {allocatedMB} MB");
    })
    .AddCheck("cpu", () =>
    {
        var process = System.Diagnostics.Process.GetCurrentProcess();
        var cpuUsage = process.TotalProcessorTime.TotalMilliseconds;

        return cpuUsage < 10000
            ? HealthCheckResult.Healthy($"CPU usage: {cpuUsage} ms")
            : HealthCheckResult.Degraded($"High CPU usage: {cpuUsage} ms");
    });

// Health Checks UI ekle
builder.Services.AddHealthChecksUI(opt =>
{
    opt.SetEvaluationTimeInSeconds(30);
    opt.MaximumHistoryEntriesPerEndpoint(50);
    opt.SetApiMaxActiveRequests(1);
    opt.AddHealthCheckEndpoint("GymProject API", "/health");
}).AddInMemoryStorage();

builder.Services.AddSingleton<FileLoggerService>();
builder.Services.AddSingleton<PerformanceLoggerService>();
builder.Services.AddSingleton<ILogService, FileLoggerService>();
builder.Services.AddSingleton<Stopwatch>();

// Environment'a göre TokenOptions'ı al
var tokenOptions = builder.Configuration.GetSection($"TokenOptions:{environment}").Get<TokenOptions>();

if (tokenOptions == null)
{
    throw new InvalidOperationException($"'{environment}' environment'ı için TokenOptions bulunamadı!");
}

// SecurityKey'i environment helper ile güvenli şekilde al
var secureKey = EnvironmentSecurityHelper.GetSecurityKey(environment, tokenOptions.SecurityKey);

// SecurityKey güvenlik kontrolü
if (!EnvironmentSecurityHelper.ValidateSecurityKey(secureKey))
{
    throw new InvalidOperationException($"'{environment}' environment'ı için SecurityKey güvenlik gereksinimlerini karşılamıyor!");
}

tokenOptions.SecurityKey = secureKey;

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidIssuer = tokenOptions.Issuer,
            ValidAudience = tokenOptions.Audience,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = SecurityKeyHelper.CreateSecurityKey(tokenOptions.SecurityKey)
        };

        // Token doğrulama hatalarını yakalayıp uygun HTTP yanıtları döndürme
        options.Events = new JwtBearerEvents
        {
            OnChallenge = context =>
            {
                // 401 Unauthorized yanıtı özelleştirme
                context.HandleResponse();
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
                var result = System.Text.Json.JsonSerializer.Serialize(new { success = false, message = "Oturum süresi dolmuş veya geçersiz. Lütfen tekrar giriş yapın." });
                return context.Response.WriteAsync(result);
            },
            OnForbidden = context =>
            {
                // 403 Forbidden yanıtı özelleştirme
                context.Response.StatusCode = 403;
                context.Response.ContentType = "application/json";
                var result = System.Text.Json.JsonSerializer.Serialize(new { success = false, message = "Bu işlem için yetkiniz bulunmamaktadır." });
                return context.Response.WriteAsync(result);
            }
        };
    });

builder.Services.AddDependencyResolvers(new ICoreModule[] { new CoreModule() });

// AspNetCoreRateLimit konfigürasyonu
builder.Services.AddOptions();
builder.Services.AddMemoryCache();
builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection("IpRateLimiting"));
builder.Services.Configure<IpRateLimitPolicies>(builder.Configuration.GetSection("IpRateLimitPolicies"));
builder.Services.AddInMemoryRateLimiting();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Swagger her zaman kullanılabilir
app.UseSwagger();
app.UseSwaggerUI();

app.ConfigureCustomExceptionMiddleware();

// Environment'a göre CORS politikasını uygula
app.UseCors("AllowSpecificOrigins");

// Development ortamında HTTPS redirect'i devre dışı bırak (mobil erişim için)
if (environment != "dev")
{
    app.UseHttpsRedirection();
}

// AspNetCoreRateLimit middleware'i ekleniyor
app.UseIpRateLimiting();

app.UseAuthentication();
app.UseAuthorization();

// Health Check endpoints
app.MapHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions()
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions()
{
    Predicate = check => check.Tags.Contains("ready"),
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions()
{
    Predicate = _ => false,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

// Health Check UI
app.MapHealthChecksUI(options =>
{
    options.UIPath = "/health-ui";
    options.ApiPath = "/health-ui-api";
});

app.MapControllers();

app.Run();