# Normal DAL'ları güncellemek için PowerShell script'i
# Bu DAL'lar EfEntityRepositoryBase'den inherit ediyor

$normalDals = @(
    "EfCityDal",
    "EfTownDal", 
    "EfCompanyAdressDal",
    "EfCompanyUserDal",
    "EfUserCompanyDal",
    "EfOperationClaimDal",
    "EfUserOperationClaimDal",
    "EfSystemExerciseDal",
    "EfExerciseCategoryDal",
    "EfLicensePackageDal",
    "EfWorkoutProgramDayDal",
    "EfWorkoutProgramExerciseDal"
)

foreach ($dalName in $normalDals) {
    $filePath = "DataAccess\Concrete\EntityFramework\$dalName.cs"
    
    if (Test-Path $filePath) {
        Write-Host "Updating $dalName..." -ForegroundColor Green
        
        # Dosyayı oku
        $content = Get-Content $filePath -Raw
        
        # Constructor ekle (eğer yoksa)
        if ($content -notmatch "public $dalName\(GymContext context\)") {
            # Class tanımından sonra constructor ekle
            $pattern = "(public class $dalName : EfEntityRepositoryBase<[^>]+>, [^{]+\{)"
            $replacement = "`$1`n        // Constructor injection ile DbContext alıyoruz`n        public $dalName(GymContext context) : base(context)`n        {`n        }`n`n        // Parametre olmayan constructor (geriye dönük uyumluluk için)`n        public $dalName()`n        {`n        }`n"
            
            $content = $content -replace $pattern, $replacement
        }
        
        # using (GymContext context = new GymContext()) ifadelerini güncelle
        $content = $content -replace "using \(GymContext context = new GymContext\(\)\)", "// Eğer DI'dan context geliyorsa onu kullan, yoksa yeni oluştur`n            var context = _context ?? new GymContext();`n            try"
        $content = $content -replace "using \(var context = new GymContext\(\)\)", "// Eğer DI'dan context geliyorsa onu kullan, yoksa yeni oluştur`n            var context = _context ?? new GymContext();`n            try"
        
        # Dosyayı kaydet
        Set-Content $filePath $content -Encoding UTF8
        
        Write-Host "$dalName updated successfully!" -ForegroundColor Yellow
    } else {
        Write-Host "File not found: $filePath" -ForegroundColor Red
    }
}

Write-Host "All normal DALs updated!" -ForegroundColor Cyan
