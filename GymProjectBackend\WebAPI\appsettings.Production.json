{"ConnectionStrings": {"dev": "Server=${DB_SERVER};Database=${DB_NAME_DEV};User Id=${DB_USER};Password=${DB_PASSWORD};Trusted_Connection=false;Encrypt=True;TrustServerCertificate=True;Max Pool Size=100;Min Pool Size=5;Connection Timeout=30;Command Timeout=30;Pooling=true", "staging": "Server=${DB_SERVER};Database=${DB_NAME_STAGING};User Id=${DB_USER};Password=${DB_PASSWORD};Trusted_Connection=false;Encrypt=True;TrustServerCertificate=True;Max Pool Size=200;Min Pool Size=10;Connection Timeout=30;Command Timeout=30;Pooling=true", "canlı": "Server=${DB_SERVER};Database=${DB_NAME_PROD};User Id=${DB_USER};Password=${DB_PASSWORD};Trusted_Connection=false;Encrypt=True;TrustServerCertificate=True;Max Pool Size=300;Min Pool Size=20;Connection Timeout=30;Command Timeout=30;Pooling=true"}, "Environment": "canlı", "AllowedOrigins": {"dev": ["http://localhost:4200", "https://localhost:4200"], "staging": ["https://staging.gymkod.com", "https://test.gymkod.com"], "canlı": ["https://admin.gymkod.com", "https://www.gymkod.com"]}, "TokenOptions": {"Audience": "${JWT_AUDIENCE}", "Issuer": "${JWT_ISSUER}", "AccessTokenExpiration": 60, "RefreshTokenExpiration": 10080, "SecurityKey": "${JWT_SECURITY_KEY}"}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "System": "Warning"}, "Console": {"IncludeScopes": false}, "File": {"Path": "/var/log/gymproject/", "FileName": "gymproject-{Date}.log", "LogLevel": {"Default": "Information", "Microsoft": "Warning", "System": "Warning"}}}, "AllowedHosts": "*", "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*", "Period": "1h", "Limit": 1000}, {"Endpoint": "*/auth/*", "Period": "1m", "Limit": 10}]}, "IpRateLimitPolicies": {"IpRules": [{"Ip": "127.0.0.1", "Rules": [{"Endpoint": "*", "Period": "1m", "Limit": 1000}]}]}, "HealthChecks": {"UI": {"HealthChecksUIEnabled": true, "HealthChecksUIPath": "/health-ui", "ApiPath": "/health", "EvaluationTimeInSeconds": 30, "MinimumSecondsBetweenFailureNotifications": 300}}, "Kestrel": {"Limits": {"MaxConcurrentConnections": 1000, "MaxConcurrentUpgradedConnections": 1000, "MaxRequestBodySize": 10485760, "KeepAliveTimeout": "00:02:00", "RequestHeadersTimeout": "00:00:30"}}}