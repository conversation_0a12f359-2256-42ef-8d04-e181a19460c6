﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCityDal: EfEntityRepositoryBase<City, GymContext>, ICityDal
    {
        // Constructor injection ile DbContext alıyoruz
        public EfCityDal(GymContext context) : base(context)
        {
        }

        // Parametre olmayan constructor (geriye dönük uyumluluk için)
        public EfCityDal()
        {
        }
    }
}
