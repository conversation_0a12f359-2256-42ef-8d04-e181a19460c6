===============================================================================
                    GYM PROJECT ENVIRONMENT CONFIGURATION
                         KULLANIM KILAVUZU (GÜNCEL)
===============================================================================

STAGING CORS SORUNU ÇÖZÜLDÜ!
Frontend: staging.gymkod.com
Backend API: stagingapi.gymkod.com

===============================================================================
                              BACKEND İŞLEMLERİ
===============================================================================

1. ENVIRONMENT DEĞİŞTİRME
-------------------------
Dosya: GymProjectBackend/WebAPI/appsettings.json

SADECE BU SATIRI DEĞİŞTİRİN:
{
  "Environment": "dev"        // Development için
  "Environment": "staging"    // Staging için  
  "Environment": "canlı"      // Production için
}

2. PUBLISH İŞLEMİ
-----------------
1. WebAPI projesine SAĞ TIKLAYIN
2. "Publish" seçin
3. Klasöre çıkarın
4. Dosyaları sunucuya yükleyin

===============================================================================
                             FRONTEND İŞLEMLERİ
===============================================================================

1. BUILD KOMUTLARI
------------------
Development için:
ng build --configuration=development

Staging için:
ng build --configuration=staging

Production için:
ng build --configuration=production

===============================================================================
                           ENVIRONMENT DETAYLARI
===============================================================================

DEVELOPMENT (dev)
-----------------
Backend:
- Database: GymProject (Trusted Connection)
- CORS: http://localhost:4200
- JWT Issuer: http://localhost:5165
- Token Süresi: 60 dakika

Frontend:
- API URL: http://localhost:5165/api/
- Production Mode: false

STAGING (staging) - GÜNCEL
--------------------------
Backend:
- Database: Staging (SQL Authentication)
- CORS: https://staging.gymkod.com
- JWT Issuer: https://staging.gymkod.com
- JWT Audience: https://stagingapi.gymkod.com
- Token Süresi: 15 dakika

Frontend:
- API URL: https://stagingapi.gymkod.com/api/
- Production Mode: true

PRODUCTION (canlı)
------------------
Backend:
- Database: GymProject (SQL Authentication)
- CORS: https://admin.gymkod.com
- JWT Issuer: https://admin.gymkod.com
- Token Süresi: 15 dakika

Frontend:
- API URL: https://admin.gymkod.com/api/
- Production Mode: true

===============================================================================
                              ADIM ADIM REHBER
===============================================================================

STAGING'E GEÇİŞ (GÜNCEL)
------------------------
1. appsettings.json açın
2. "Environment": "staging" yazın
3. WebAPI'ye sağ tıklayın → Publish
4. stagingapi.gymkod.com sunucusuna yükleyin
5. Terminal'de: ng build --configuration=staging
6. staging.gymkod.com sunucusuna yükleyin

PRODUCTION'A GEÇİŞ
------------------
1. appsettings.json açın
2. "Environment": "canlı" yazın
3. WebAPI'ye sağ tıklayın → Publish
4. admin.gymkod.com sunucusuna yükleyin
5. Terminal'de: ng build --configuration=production
6. admin.gymkod.com sunucusuna yükleyin

===============================================================================
                               ÖNEMLİ UYARILAR
===============================================================================

YAPMANIZ GEREKENLER:
✓ Sadece "Environment" değerini değiştirin
✓ Doğru build komutunu kullanın
✓ WebAPI'ye sağ tıklayıp publish yapın
✓ Frontend ve Backend'i doğru sunuculara yükleyin

YAPMAMANIZ GEREKENLER:
✗ Connection string'leri manuel değiştirmeyin
✗ CORS ayarlarını manuel değiştirmeyin
✗ JWT ayarlarını manuel değiştirmeyin

===============================================================================
                              SUNUCU YAPISI
===============================================================================

STAGING:
- Frontend: staging.gymkod.com (Angular uygulaması)
- Backend: stagingapi.gymkod.com (API sunucusu)

PRODUCTION:
- Frontend: admin.gymkod.com (Angular uygulaması)
- Backend: admin.gymkod.com (API sunucusu - aynı domain)

===============================================================================
Son Güncelleme: 01.07.2025 - CORS sorunu çözüldü
===============================================================================
