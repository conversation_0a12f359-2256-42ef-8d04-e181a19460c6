﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyDal : EfEntityRepositoryBase<Company, GymContext>, ICompanyDal
    {
        // Constructor injection ile DbContext alıyoruz
        public EfCompanyDal(GymContext context) : base(context)
        {
        }

        // Parametre olmayan constructor (geriye dönük uyumluluk için)
        public EfCompanyDal()
        {
        }

        public List<ActiveCompanyDetailDto> GetActiveCompanies()
        {
            // Eğer DI'dan context geliyorsa onu kullan, yoksa yeni oluştur
            var context = _context ?? new GymContext();
            try
            {
                var result = from c in context.Companies
                             where c.IsActive == true
                             select new ActiveCompanyDetailDto
                             {
                               CompanyID = c.CompanyID,
                               CompanyName = c.CompanyName,
                               PhoneNumber = c.PhoneNumber,
                               IsActive = c.IsActive,
                              
                             };
                return result.ToList();
            }
            finally
            {
                // Eğer context'i biz oluşturduysak dispose et
                if (_context == null && context != null)
                {
                    context.Dispose();
                }
            }
        }
    }
}
